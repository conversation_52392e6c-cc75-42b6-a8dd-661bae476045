<script setup lang="ts">
const { t } = useI18n()

const features = computed(() => [
  {
    icon: 'mingcute:ai-fill',
    title: t('features.ai.title', 'AI Tiên Tiến'),
    description: t('features.ai.description', '<PERSON><PERSON> dụng công nghệ AI mới nhất để tạo ra video chất lượng cao với độ chính xác đáng kinh ngạc.')
  },
  {
    icon: 'lucide:zap',
    title: t('features.speed.title', 'Tạo <PERSON>'),
    description: t('features.speed.description', 'Chuyển đổi ý tưởng thành video chỉ trong vài giây. Không cần chờ đợi lâu.')
  },
  {
    icon: 'lucide:palette',
    title: t('features.creative.title', 'Sáng Tạo Không Giới Hạn'),
    description: t('features.creative.description', 'Tạo ra video trong mọi phong cách, từ hoạt hình đến thự<PERSON> tế, từ nghệ thuật đến chuyên nghiệp.')
  },
  {
    icon: 'lucide:monitor',
    title: t('features.quality.title', 'Chất Lượng Cao'),
    description: t('features.quality.description', 'Video đầu ra với độ phân giải cao, chuyển động mượt mà và chi tiết sắc nét.')
  },
  {
    icon: 'lucide:users',
    title: t('features.collaboration.title', 'Cộng Tác Dễ Dàng'),
    description: t('features.collaboration.description', 'Chia sẻ và cộng tác trên các dự án video với team của bạn một cách dễ dàng.')
  },
  {
    icon: 'lucide:download',
    title: t('features.export.title', 'Xuất Đa Định Dạng'),
    description: t('features.export.description', 'Xuất video theo nhiều định dạng khác nhau phù hợp với mọi nền tảng và mục đích sử dụng.')
  }
])
</script>

<template>
  <section class="py-24 bg-gradient-to-b from-background to-muted/30">
    <UContainer class="max-w-7xl">
      <Motion
        :initial="{
          opacity: 0,
          y: 50
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8
        }"
        class="text-center mb-16"
      >
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
          {{ t('features.title', 'Tính Năng Nổi Bật') }}
        </h2>
        <p class="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
          {{ t('features.subtitle', 'Khám phá những tính năng mạnh mẽ giúp bạn tạo ra video AI chuyên nghiệp') }}
        </p>
      </Motion>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-16">
        <Motion
          v-for="(feature, index) in features"
          :key="index"
          :initial="{
            opacity: 0,
            y: 50,
            scale: 0.9
          }"
          :animate="{
            opacity: 1,
            y: 0,
            scale: 1
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.1 + 0.2
          }"
        >
          <div class="group p-8 rounded-2xl bg-card border border-border hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 hover:-translate-y-1">
            <div class="flex items-center justify-center w-16 h-16 rounded-2xl bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300 mb-6">
              <UIcon 
                :name="feature.icon" 
                class="w-8 h-8 text-primary group-hover:text-primary group-hover:scale-110 transition-all duration-300" 
              />
            </div>
            
            <h3 class="text-xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">
              {{ feature.title }}
            </h3>
            
            <p class="text-muted-foreground leading-relaxed">
              {{ feature.description }}
            </p>
          </div>
        </Motion>
      </div>

      <!-- Call to action at the bottom of features -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 0.8
        }"
        class="text-center mt-16"
      >
        <div class="p-8 rounded-2xl bg-gradient-to-r from-primary/10 to-violet-500/10 border border-primary/20">
          <h3 class="text-2xl font-bold mb-4">
            {{ t('features.cta.title', 'Sẵn sàng bắt đầu?') }}
          </h3>
          <p class="text-muted-foreground mb-6">
            {{ t('features.cta.description', 'Trải nghiệm sức mạnh của AI trong việc tạo video ngay hôm nay') }}
          </p>
          <UButton
            size="lg"
            color="primary"
            variant="solid"
            :label="t('features.cta.button', 'Tạo Video Đầu Tiên')"
            to="/app"
            class="px-8 py-3"
            icon="mingcute:ai-fill"
          />
        </div>
      </Motion>
    </UContainer>
  </section>
</template>

<style scoped>
/* Additional hover effects and transitions */
.group:hover {
  transform: translateY(-4px);
}

/* Gradient text effect for headings */
h2 {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-violet-500)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Card shadow animation */
@keyframes card-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--color-primary-500), 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(var(--color-primary-500), 0.2);
  }
}

.group:hover {
  animation: card-glow 2s ease-in-out infinite;
}
</style>
<script setup lang="ts">
const { t } = useI18n()

const links = [
  {
    title: t('footer.product.title', 'Sản <PERSON>ẩ<PERSON>'),
    items: [
      { label: t('footer.product.features', '<PERSON><PERSON><PERSON>'), to: '#features' },
      { label: t('footer.product.howto', '<PERSON><PERSON><PERSON>'), to: '#demo-section' },
      { label: t('footer.product.pricing', 'Bảng Giá'), to: '/pricing' },
      { label: t('footer.product.app', 'Ứng Dụng'), to: '/app' }
    ]
  },
  {
    title: t('footer.company.title', 'Công Ty'),
    items: [
      { label: t('footer.company.about', '<PERSON><PERSON>úng Tô<PERSON>'), to: '/about' },
      { label: t('footer.company.blog', 'Blog'), to: '/blog' },
      { label: t('footer.company.careers', '<PERSON>yể<PERSON>'), to: '/careers' },
      { label: t('footer.company.contact', '<PERSON><PERSON><PERSON>'), to: '/contact' }
    ]
  },
  {
    title: t('footer.legal.title', '<PERSON><PERSON><PERSON>'),
    items: [
      { label: t('footer.legal.privacy', 'Chính <PERSON>ách Bảo Mật'), to: '/privacy' },
      { label: t('footer.legal.terms', 'Điều Khoản Dịch Vụ'), to: '/terms' },
      { label: t('footer.legal.cookies', 'Chính Sách Cookie'), to: '/cookies' }
    ]
  },
  {
    title: t('footer.support.title', 'Hỗ Trợ'),
    items: [
      { label: t('footer.support.help', 'Trung Tâm Trợ Giúp'), to: '/help' },
      { label: t('footer.support.api', 'API Docs'), to: '/api-docs' },
      { label: t('footer.support.status', 'Trạng Thái Hệ Thống'), to: '/status' }
    ]
  }
]

const socialLinks = [
  { icon: 'i-simple-icons-discord', to: 'https://discord.com/channels/1396217701449338972/1396219648298717225', label: 'Discord' },
  { icon: 'i-simple-icons-twitter', to: 'https://twitter.com/geminigen_ai', label: 'Twitter' },
  { icon: 'i-simple-icons-facebook', to: 'https://facebook.com/geminigen.ai', label: 'Facebook' },
  { icon: 'i-simple-icons-youtube', to: 'https://youtube.com/@geminigen', label: 'YouTube' }
]
</script>

<template>
  <footer class="bg-card border-t border-border">
    <UContainer class="py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
        <!-- Brand section -->
        <div class="lg:col-span-2">
          <Motion
            :initial="{
              opacity: 0,
              y: 20
            }"
            :animate="{
              opacity: 1,
              y: 0
            }"
            :transition="{
              duration: 0.6
            }"
          >
            <div class="flex items-center gap-3 mb-4">
              <BaseLogo class="w-8 h-8" />
              <span class="text-xl font-bold">GeminiGen AI</span>
            </div>
            <p class="text-muted-foreground mb-6 leading-relaxed">
              {{ t('footer.description', 'Tạo video AI chất lượng cao với công nghệ tiên tiến nhất. Chuyển đổi ý tưởng thành video chuyên nghiệp chỉ trong vài giây.') }}
            </p>
            
            <!-- Social links -->
            <div class="flex gap-4">
              <UButton
                v-for="social in socialLinks"
                :key="social.label"
                :icon="social.icon"
                :to="social.to"
                :aria-label="social.label"
                color="gray"
                variant="ghost"
                size="sm"
                target="_blank"
                class="hover:text-primary hover:bg-primary/10"
              />
            </div>
          </Motion>
        </div>

        <!-- Links sections -->
        <Motion
          v-for="(section, index) in links"
          :key="section.title"
          :initial="{
            opacity: 0,
            y: 20
          }"
          :animate="{
            opacity: 1,
            y: 0
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.1 + 0.2
          }"
          class="space-y-4"
        >
          <h3 class="font-semibold text-foreground">
            {{ section.title }}
          </h3>
          <ul class="space-y-2">
            <li v-for="item in section.items" :key="item.label">
              <UButton
                :label="item.label"
                :to="item.to"
                variant="ghost"
                color="gray"
                size="sm"
                class="justify-start p-0 h-auto font-normal text-muted-foreground hover:text-primary"
              />
            </li>
          </ul>
        </Motion>
      </div>

      <!-- Newsletter signup -->
      <Motion
        :initial="{
          opacity: 0,
          y: 20
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.6,
          delay: 0.8
        }"
        class="mt-12 pt-8 border-t border-border"
      >
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div>
            <h3 class="font-semibold mb-2">
              {{ t('footer.newsletter.title', 'Nhận Thông Tin Mới Nhất') }}
            </h3>
            <p class="text-sm text-muted-foreground">
              {{ t('footer.newsletter.description', 'Đăng ký để nhận tin tức về tính năng mới và cập nhật sản phẩm') }}
            </p>
          </div>
          <div class="flex gap-2 max-w-md w-full">
            <UInput
              :placeholder="t('footer.newsletter.placeholder', 'Nhập email của bạn')"
              class="flex-1"
              size="md"
            />
            <UButton
              :label="t('footer.newsletter.subscribe', 'Đăng Ký')"
              color="primary"
              size="md"
              class="shrink-0"
            />
          </div>
        </div>
      </Motion>

      <!-- Bottom section -->
      <Motion
        :initial="{
          opacity: 0,
          y: 20
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.6,
          delay: 1
        }"
        class="mt-12 pt-8 border-t border-border flex flex-col md:flex-row md:items-center md:justify-between gap-4"
      >
        <p class="text-sm text-muted-foreground">
          {{ t('footer.copyright', '© 2024 GeminiGen AI. Tất cả quyền được bảo lưu.') }}
        </p>
        
        <div class="flex items-center gap-6">
          <UButton
            :label="t('footer.language', 'Tiếng Việt')"
            icon="lucide:globe"
            variant="ghost"
            color="gray"
            size="sm"
            class="text-muted-foreground"
          />
          <ColorModeButton />
        </div>
      </Motion>
    </UContainer>
  </footer>
</template>

<style scoped>
/* Hover effects for links */
a:hover {
  transition: color 0.2s ease;
}

/* Newsletter input focus effects */
.UInput:focus-within {
  ring: 2px solid rgb(var(--color-primary-500));
}
</style>
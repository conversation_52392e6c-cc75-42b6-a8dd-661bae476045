<script setup lang="ts">
const { t } = useI18n()

const steps = computed(() => [
  {
    step: '01',
    icon: 'lucide:edit-3',
    title: t('howto.step1.title', '<PERSON><PERSON> Tả Ý Tưởng'),
    description: t('howto.step1.description', 'Viết mô tả chi tiết về video bạn muốn tạo. <PERSON><PERSON> gồm cảnh quay, nhân vật, hành động và phong cách.')
  },
  {
    step: '02',
    icon: 'lucide:settings',
    title: t('howto.step2.title', 'Tùy Chỉnh Cài Đặt'),
    description: t('howto.step2.description', 'Chọn độ phân giải, tỷ lệ khung hình, phong cách và các tham số khác phù hợp với mục đích sử dụng.')
  },
  {
    step: '03',
    icon: 'mingcute:ai-fill',
    title: t('howto.step3.title', 'AI Xử Lý'),
    description: t('howto.step3.description', 'Trí tuệ nhân tạo sẽ phân tích yêu cầu và tạo ra video với chất lượng cao nhất trong vài giây.')
  },
  {
    step: '04',
    icon: 'lucide:download',
    title: t('howto.step4.title', 'Tải Về & Chia Sẻ'),
    description: t('howto.step4.description', 'Tải video về máy tính hoặc chia sẻ trực tiếp lên các nền tảng mạng xã hội yêu thích.')
  }
])

const examples = [
  {
    prompt: 'Một con mèo đang chạy qua cánh đồng hoa anh đào trong hoàng hôn',
    video: 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-cat-running.mp4',
    thumbnail: 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-cat-running-thumb.jpg'
  },
  {
    prompt: 'Người đàn ông đang lướt sóng trên biển xanh trong ngày nắng đẹp',
    video: 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-surfing.mp4',
    thumbnail: 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-surfing-thumb.jpg'
  },
  {
    prompt: 'Thành phố tương lai với ôtô bay và tòa nhà cao chọc trời',
    video: 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-future-city.mp4',
    thumbnail: 'https://pub-ad89192f494c4db181f122a77691d67e.r2.dev/example-future-city-thumb.jpg'
  }
]
</script>

<template>
  <section id="demo-section" class="py-24 bg-background">
    <UContainer class="max-w-7xl">
      <!-- How to create section -->
      <Motion
        :initial="{
          opacity: 0,
          y: 50
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8
        }"
        class="text-center mb-16"
      >
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
          {{ t('howto.title', 'Cách Tạo Video AI') }}
        </h2>
        <p class="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
          {{ t('howto.subtitle', 'Chỉ với 4 bước đơn giản, bạn có thể tạo ra video AI chuyên nghiệp') }}
        </p>
      </Motion>

      <!-- Steps -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
        <Motion
          v-for="(step, index) in steps"
          :key="index"
          :initial="{
            opacity: 0,
            y: 50,
            scale: 0.9
          }"
          :animate="{
            opacity: 1,
            y: 0,
            scale: 1
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.2
          }"
        >
          <div class="text-center group">
            <!-- Step number -->
            <div class="relative mb-6">
              <div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-r from-primary-500 to-violet-500 flex items-center justify-center text-white font-bold text-xl group-hover:scale-110 transition-transform duration-300">
                {{ step.step }}
              </div>
              <!-- Connection line (except for last step) -->
              <div 
                v-if="index < steps.length - 1"
                class="hidden lg:block absolute top-10 left-1/2 w-full h-0.5 bg-gradient-to-r from-primary-500/50 to-violet-500/50"
                style="transform: translateX(50%); z-index: -1;"
              />
            </div>

            <!-- Icon -->
            <div class="flex items-center justify-center w-16 h-16 mx-auto rounded-2xl bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300 mb-4">
              <UIcon 
                :name="step.icon" 
                class="w-8 h-8 text-primary group-hover:scale-110 transition-transform duration-300" 
              />
            </div>

            <!-- Content -->
            <h3 class="text-xl font-bold mb-4">
              {{ step.title }}
            </h3>
            <p class="text-muted-foreground">
              {{ step.description }}
            </p>
          </div>
        </Motion>
      </div>

      <!-- Examples section -->
      <Motion
        :initial="{
          opacity: 0,
          y: 50
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 0.5
        }"
        class="text-center mb-12"
      >
        <h3 class="text-2xl md:text-3xl font-bold mb-4">
          {{ t('howto.examples.title', 'Ví Dụ Video AI') }}
        </h3>
        <p class="text-lg text-muted-foreground">
          {{ t('howto.examples.subtitle', 'Xem những video được tạo từ prompt đơn giản') }}
        </p>
      </Motion>

      <!-- Video examples grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <Motion
          v-for="(example, index) in examples"
          :key="index"
          :initial="{
            opacity: 0,
            y: 30,
            scale: 0.95
          }"
          :animate="{
            opacity: 1,
            y: 0,
            scale: 1
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.2 + 0.7
          }"
        >
          <div class="group rounded-2xl overflow-hidden bg-card border border-border hover:border-primary/50 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10">
            <!-- Video container -->
            <div class="relative aspect-video bg-muted/50 overflow-hidden">
              <video
                :poster="example.thumbnail"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                controls
                preload="metadata"
              >
                <source :src="example.video" type="video/mp4">
                <img :src="example.thumbnail" :alt="example.prompt" class="w-full h-full object-cover">
              </video>
            </div>
            
            <!-- Prompt text -->
            <div class="p-6">
              <p class="text-sm text-muted-foreground italic">
                "{{ example.prompt }}"
              </p>
            </div>
          </div>
        </Motion>
      </div>

      <!-- Call to action -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 1.2
        }"
        class="text-center mt-16"
      >
        <UButton
          size="lg"
          color="primary"
          variant="solid"
          :label="t('howto.cta', 'Thử Tạo Video Ngay')"
          to="/app"
          class="px-8 py-3"
          icon="mingcute:ai-fill"
        />
      </Motion>
    </UContainer>
  </section>
</template>

<style scoped>
/* Gradient text effect */
h2 {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-violet-500)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Step connection line animation */
@keyframes line-draw {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

.group:hover + .group .absolute {
  animation: line-draw 0.5s ease-in-out;
}

/* Video hover effects */
video:hover {
  transform: scale(1.02);
}

/* Custom responsive adjustments */
@media (max-width: 1024px) {
  .absolute {
    display: none !important;
  }
}
</style>
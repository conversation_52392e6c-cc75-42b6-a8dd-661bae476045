<script setup lang="ts">
const { t } = useI18n()

const faqData = computed(() => ({
  title: t('faq.title', 'Câu Hỏi Thường Gặp'),
  description: t('faq.description', 'Tìm câu trả lời cho những thắc mắc phổ biến về việc tạo video AI'),
  categories: [
    {
      title: t('faq.general.title', 'Chung'),
      questions: [
        {
          title: t('faq.general.q1.title', 'Video AI được tạo như thế nào?'),
          content: t('faq.general.q1.content', '<PERSON>úng tôi sử dụng công nghệ AI tiên tiến để phân tích prompt của bạn và tạo ra video với chất lượng cao. Quá trình này diễn ra hoàn toàn tự động và chỉ mất vài giây.')
        },
        {
          title: t('faq.general.q2.title', 'T<PERSON><PERSON> có thể tạo video dài bao nhiêu?'),
          content: t('faq.general.q2.content', '<PERSON><PERSON><PERSON> tại, mỗi video có thể dài từ 3-10 giây. Chúng tôi đang phát triển tính năng tạo video dài hơn trong tương lai gần.')
        },
        {
          title: t('faq.general.q3.title', 'Có giới hạn số lượng video tạo không?'),
          content: t('faq.general.q3.content', 'Mỗi gói dịch vụ có số lượng credit khác nhau. Bạn có thể xem chi tiết tại trang Pricing hoặc nâng cấp gói để có thêm credit.')
        }
      ]
    },
    {
      title: t('faq.quality.title', 'Chất Lượng'),
      questions: [
        {
          title: t('faq.quality.q1.title', 'Độ phân giải video như thế nào?'),
          content: t('faq.quality.q1.content', 'Video được tạo với độ phân giải HD (1280x720) và có thể xuất ra nhiều định dạng khác nhau như MP4, WebM để phù hợp với nhu cầu sử dụng.')
        },
        {
          title: t('faq.quality.q2.title', 'Tôi có thể chỉnh sửa video sau khi tạo không?'),
          content: t('faq.quality.q2.content', 'Hiện tại bạn có thể tải video về và sử dụng phần mềm chỉnh sửa video. Chúng tôi đang phát triển tính năng chỉnh sửa trực tiếp trên nền tảng.')
        },
        {
          title: t('faq.quality.q3.title', 'Video có âm thanh không?'),
          content: t('faq.quality.q3.content', 'Video AI được tạo không có âm thanh. Bạn có thể thêm nhạc nền hoặc giọng nói bằng các công cụ chỉnh sửa video khác.')
        }
      ]
    },
    {
      title: t('faq.pricing.title', 'Giá Cả'),
      questions: [
        {
          title: t('faq.pricing.q1.title', 'Có dùng thử miễn phí không?'),
          content: t('faq.pricing.q1.content', 'Có! Bạn sẽ nhận được một số credit miễn phí khi đăng ký. Điều này cho phép bạn trải nghiệm dịch vụ trước khi quyết định mua gói trả phí.')
        },
        {
          title: t('faq.pricing.q2.title', 'Các phương thức thanh toán được hỗ trợ?'),
          content: t('faq.pricing.q2.content', 'Chúng tôi hỗ trợ thanh toán qua thẻ tín dụng, PayPal và một số phương thức thanh toán điện tử phổ biến.')
        },
        {
          title: t('faq.pricing.q3.title', 'Tôi có thể hủy subscription bất cứ lúc nào không?'),
          content: t('faq.pricing.q3.content', 'Có, bạn có thể hủy subscription bất cứ lúc nào. Credit còn lại sẽ được giữ lại cho đến khi hết hạn.')
        }
      ]
    }
  ]
}))

const selectedCategory = ref(0)
</script>

<template>
  <section class="py-24 bg-gradient-to-b from-muted/30 to-background">
    <UContainer class="max-w-6xl">
      <Motion
        :initial="{
          opacity: 0,
          y: 50
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8
        }"
        class="text-center mb-16"
      >
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
          {{ faqData.title }}
        </h2>
        <p class="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
          {{ faqData.description }}
        </p>
      </Motion>

      <!-- Category tabs -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.6,
          delay: 0.2
        }"
      >
        <div class="flex flex-wrap justify-center gap-4 mb-12">
          <button
            v-for="(category, index) in faqData.categories"
            :key="index"
            @click="selectedCategory = index"
            class="px-6 py-3 rounded-full font-medium transition-all duration-300"
            :class="selectedCategory === index 
              ? 'bg-primary text-primary-foreground shadow-lg' 
              : 'bg-card text-muted-foreground hover:bg-primary/10 hover:text-primary border border-border'"
          >
            {{ category.title }}
          </button>
        </div>
      </Motion>

      <!-- FAQ Content -->
      <Motion
        :key="selectedCategory"
        :initial="{
          opacity: 0,
          x: 20
        }"
        :animate="{
          opacity: 1,
          x: 0
        }"
        :transition="{
          duration: 0.5
        }"
      >
        <div class="space-y-4">
          <Motion
            v-for="(question, index) in faqData.categories[selectedCategory].questions"
            :key="index"
            :initial="{
              opacity: 0,
              y: 20
            }"
            :animate="{
              opacity: 1,
              y: 0
            }"
            :transition="{
              duration: 0.4,
              delay: index * 0.1
            }"
          >
            <UAccordion
              :items="[{
                label: question.title,
                content: question.content,
                defaultOpen: index === 0
              }]"
              :ui="{
                wrapper: 'space-y-0',
                item: {
                  base: 'border border-border rounded-lg overflow-hidden',
                  padding: 'p-0'
                },
                trigger: {
                  base: 'flex items-center gap-3 w-full text-left p-6 hover:bg-muted/50 transition-colors duration-200',
                  label: 'text-lg font-semibold'
                },
                content: {
                  base: 'text-muted-foreground px-6 pb-6 pt-0 leading-relaxed'
                },
                trailingIcon: {
                  base: 'w-5 h-5 text-muted-foreground transition-transform duration-200'
                }
              }"
              class="mb-4"
            />
          </Motion>
        </div>
      </Motion>

      <!-- Contact support -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 0.6
        }"
        class="text-center mt-16"
      >
        <div class="p-8 rounded-2xl bg-gradient-to-r from-primary/10 to-violet-500/10 border border-primary/20">
          <h3 class="text-xl font-bold mb-4">
            {{ t('faq.contact.title', 'Vẫn có thắc mắc?') }}
          </h3>
          <p class="text-muted-foreground mb-6">
            {{ t('faq.contact.description', 'Đội ngũ hỗ trợ của chúng tôi sẵn sàng giúp bạn 24/7') }}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <UButton
              size="lg"
              color="primary"
              variant="solid"
              :label="t('faq.contact.email', 'Gửi Email')"
              icon="lucide:mail"
              to="mailto:<EMAIL>"
              class="px-6"
            />
            <UButton
              size="lg"
              color="primary"
              variant="outline"
              :label="t('faq.contact.discord', 'Join Discord')"
              icon="i-simple-icons-discord"
              to="https://discord.com/channels/1396217701449338972/1396219648298717225"
              target="_blank"
              class="px-6"
            />
          </div>
        </div>
      </Motion>
    </UContainer>
  </section>
</template>

<style scoped>
/* Gradient text effect */
h2 {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-violet-500)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Tab hover effects */
button:hover {
  transform: translateY(-2px);
}

/* Accordion custom styling */
.UAccordion {
  background: rgb(var(--color-card));
}
</style>
<template>
  <UCard
    :ui="{
      body: 'p-6'
    }"
    class="h-full"
  >
    <div class="flex flex-col items-center justify-center h-full space-y-6">
      <!-- Training Animation -->
      <div class="relative">
        <div class="w-24 h-24 bg-gradient-to-r from-warning-400 to-warning-600 rounded-full flex items-center justify-center">
          <UIcon
            name="fluent:person-voice-24-filled"
            class="w-12 h-12 text-white"
          />
        </div>
        <!-- Spinning border -->
        <div class="absolute inset-0 w-24 h-24 border-4 border-warning-500 border-t-transparent rounded-full animate-spin" />
        <!-- Pulsing effect -->
        <div class="absolute inset-0 w-24 h-24 bg-warning-500/20 rounded-full animate-ping" />
      </div>

      <!-- Training Status -->
      <div class="text-center space-y-2">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          {{ $t('Voice Training in Progress') }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 max-w-md">
          {{ $t('Your custom voice is being trained. This process may take up to 30 minutes. You will be notified when it\'s ready.') }}
        </p>
      </div>

      <!-- Voice Details -->
      <div
        v-if="voice"
        class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 w-full max-w-sm"
      >
        <div class="space-y-2">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t('Voice Name') }}:
            </span>
            <span class="text-sm text-gray-900 dark:text-white">
              {{ voice.speaker_name }}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t('Training Type') }}:
            </span>
            <span class="text-sm text-gray-900 dark:text-white">
              {{ voice.training_type === 'instant_voice' ? $t('Instant Voice Cloning') : $t('Professional Voice Cloning') }}
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t('Status') }}:
            </span>
            <UBadge
              size="xs"
              color="warning"
              variant="subtle"
              class="animate-pulse"
            >
              {{ $t('Training') }}
            </UBadge>
          </div>
        </div>
      </div>

      <!-- Progress Indicator -->
      <div class="w-full max-w-sm">
        <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-2">
          <span>{{ $t('Training Progress') }}</span>
          <span>{{ $t('Estimated time: 5-30 minutes') }}</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div class="bg-gradient-to-r from-warning-400 to-warning-600 h-2 rounded-full animate-pulse" style="width: 45%;" />
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex space-x-3">
        <UButton
          variant="outline"
          color="gray"
          @click="$emit('close')"
        >
          {{ $t('Close') }}
        </UButton>
        <UButton
          variant="outline"
          color="primary"
          @click="$emit('refresh')"
        >
          {{ $t('Refresh Status') }}
        </UButton>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { SpeechVoice } from '~/composables/useSpeechVoices'

interface Props {
  voice?: SpeechVoice | null
}

const props = withDefaults(defineProps<Props>(), {
  voice: null
})

defineEmits<{
  'close': []
  'refresh': []
}>()
</script>

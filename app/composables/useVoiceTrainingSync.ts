import { ref, onUnmounted } from 'vue'

interface VoiceTrainingSyncOptions {
  voiceId: string
  intervalMs?: number
  maxDurationMs?: number
  targetStatuses?: number[]
  onStatusChange?: (status: number, voiceDetail: any) => void
  onComplete?: (voiceDetail: any) => void
  onError?: (error: any) => void
}

export function useVoiceTrainingSync() {
  const activeTimers = ref<Map<string, NodeJS.Timeout>>(new Map())
  const activeSyncs = ref<Set<string>>(new Set())

  const startVoiceTrainingSync = (options: VoiceTrainingSyncOptions) => {
    const {
      voiceId,
      intervalMs = 30000, // 30 seconds
      maxDurationMs = 1800000, // 30 minutes for voice training
      targetStatuses = [1, 3], // Complete (1) or Error (3)
      onStatusChange,
      onComplete,
      onError
    } = options

    // Don't start if already syncing this voice ID
    if (activeSyncs.value.has(voiceId)) {
      console.log(`🚀 ~ Voice training sync already active for voice ID: ${voiceId}`)
      return
    }

    console.log(`🚀 ~ Starting voice training sync for voice ID: ${voiceId}`)
    activeSyncs.value.add(voiceId)

    const speechVoicesStore = useSpeechVoicesStore()
    const startTime = Date.now()
    let attemptCount = 0

    const syncAttempt = async () => {
      try {
        attemptCount++
        console.log(`🚀 ~ Voice training sync attempt ${attemptCount} for voice ID: ${voiceId}`)

        // Reload voices to get updated status
        await speechVoicesStore.loadVoices(true) // Force reload

        // Find the voice in the updated list
        const voice = speechVoicesStore.voices.find(v => v.id === voiceId)

        if (voice) {
          const currentStatus = voice.status
          console.log(`🚀 ~ Current voice training status for voice ID ${voiceId}: ${currentStatus}`)

          // Call status change callback
          if (onStatusChange) {
            onStatusChange(currentStatus, voice)
          }

          // Check if we've reached target status
          if (targetStatuses.includes(currentStatus)) {
            console.log(`🚀 ~ Voice training target status reached for voice ID ${voiceId}: ${currentStatus}`)
            stopVoiceTrainingSync(voiceId)
            if (onComplete) {
              onComplete(voice)
            }
            return
          }
        } else {
          console.warn(`🚀 ~ Voice with ID ${voiceId} not found in updated voice list`)
        }

        // Check if max duration exceeded
        const elapsed = Date.now() - startTime
        if (elapsed >= maxDurationMs) {
          console.log(`🚀 ~ Voice training sync max duration exceeded for voice ID ${voiceId}`)
          stopVoiceTrainingSync(voiceId)
          return
        }

        // Schedule next sync
        const timer = setTimeout(syncAttempt, intervalMs)
        activeTimers.value.set(voiceId, timer)
      } catch (error) {
        console.error(`🚀 ~ Voice training sync error for voice ID ${voiceId}:`, error)
        if (onError) {
          onError(error)
        }

        // Check if max duration exceeded
        const elapsed = Date.now() - startTime
        if (elapsed >= maxDurationMs) {
          console.log(`🚀 ~ Voice training sync max duration exceeded after error for voice ID ${voiceId}`)
          stopVoiceTrainingSync(voiceId)
          return
        }

        // Continue syncing even after error
        const timer = setTimeout(syncAttempt, intervalMs)
        activeTimers.value.set(voiceId, timer)
      }
    }

    // Start the first sync attempt
    syncAttempt()
  }

  const stopVoiceTrainingSync = (voiceId: string) => {
    console.log(`🚀 ~ Stopping voice training sync for voice ID: ${voiceId}`)

    // Clear timer
    const timer = activeTimers.value.get(voiceId)
    if (timer) {
      clearTimeout(timer)
      activeTimers.value.delete(voiceId)
    }

    // Remove from active syncs
    activeSyncs.value.delete(voiceId)
  }

  const stopAllVoiceTrainingSyncs = () => {
    console.log('🚀 ~ Stopping all voice training syncs')

    // Clear all timers
    activeTimers.value.forEach((timer, voiceId) => {
      clearTimeout(timer)
      console.log(`🚀 ~ Cleared timer for voice ID: ${voiceId}`)
    })

    // Clear all state
    activeTimers.value.clear()
    activeSyncs.value.clear()
  }

  const isVoiceTrainingSyncing = (voiceId: string) => {
    return activeSyncs.value.has(voiceId)
  }

  const getActiveVoiceTrainingSyncs = () => {
    return Array.from(activeSyncs.value)
  }

  // Cleanup on unmount
  onUnmounted(() => {
    stopAllVoiceTrainingSyncs()
  })

  return {
    startVoiceTrainingSync,
    stopVoiceTrainingSync,
    stopAllVoiceTrainingSyncs,
    isVoiceTrainingSyncing,
    getActiveVoiceTrainingSyncs,
    activeSyncs: readonly(activeSyncs),
    activeTimers: readonly(activeTimers)
  }
}

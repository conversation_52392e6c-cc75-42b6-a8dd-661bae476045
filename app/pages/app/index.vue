<script setup lang="ts">
import {
  compareImageArrays,
  commonValidationRules
} from '~/utils/generationValidation'

interface ImageFile {
  src: string
  alt: string
  file: File
}
const { authorize } = useAuthorize()
const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const { model, models } = useImageGenModels()
const { style } = useStyles()
const { imageDimension } = useImageDimensions()
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()

const aiPhotos = [
  'https://lh3.googleusercontent.com/lB3auEv_RCugfyK2CGydHn_aeh51AgkIGjZMajy2O3DTgcMwJwuBmm2U1Bjb0Hk-vn4VfyAmmm5s4fLoHyCCKatrPzs00-X6ezg=e365-pa-nu-w3812',
  'https://lh3.googleusercontent.com/S4JOFrhu7Ot1e-Zuwh40wmo5_OCQ_jVMAAhpL7a2FFfW9so5UjZ9B8cfakGvdy6Sxkqp_PBJ64ir4bV3lXA-1EwbLEzwiVTeTkY=e365-pa-nu-w1632',
  'https://ai.google.dev/static/gemini-api/docs/images/imagen/aspect-ratios_16-9_man.png?dcb_=0.804337488779848',
  'https://lh3.googleusercontent.com/KqPPryXDAQeGsYzl0YzExjspks9g70nLghidOIaSh073TMb8j1l5QP6avh6tXPSTIMOQRc0LsCIwEE6JTZ_PQ6oDBLc8IGhU9JtazI7mo8Pi6PWN5g=h1200-rw',

  'https://lh3.googleusercontent.com/8sBjUE_gw7SImRacTXQEovaWYG-EI_m3Iz2oo1dyss9D6LvYtdtnkud-fGMCzjdzvCOAjDxct_bDrT8rTUy3xZij9D4AVNODYPv0bxPYhfLBBFDtJw=w1024-rw',

  'https://lh3.googleusercontent.com/463sZeH_lNHv8eocljw0rlCezegix3GQ3qIV9H5JCbx3jA8LHL9P2rHAMklmzUtD1hTkNhbTdA8-e8r-ZQ2Z1F9P_1GZqmR_xVxGQbDg37iamCe_6g=h1200-rw',
  'https://lh3.googleusercontent.com/q7EWp6Z5gAWVnUIgSX9-WihK9Dg6j4wy-tPKkPh-wf-JiX5-NPMXTCaR8F0oagR-obXMxFzf_KqmcgJ-owXSaO1sQlIscolqNLMT0cC779xStZBG=w1024-rw',
  'https://lh3.googleusercontent.com/bD7f5lvi8sabyhA-r5nBzmJTmcqzELG8w_Nj56_xJ5jRUwmI4bz3V1GkMR1oQK_0RypRohIZLol0v8Wd80gGXmF9e9p1i1um9oy8emEIdahREUKEzw=h1200-rw',
  'https://lh3.googleusercontent.com/-1u4Ak5P47bmfoEfXYYrasKkxTgpm4KAaEBELKZ2v-j34uibZRyudTeqTLXguLiLwgBBX_IqF_3Xuh3_Afu5EI0q2Tz600fJPktWKwoQeaheAia6=w1024-rw',
  'https://lh3.googleusercontent.com/l4ff-vf897wcld2xu4MAECVzx_uoQ8vmaIGatU4GOti81RsnZYyrq2g8ZnTFfYaTInx1BMPdrl3SexJSfPlzHjQfMRtOM7S2tNsOsUSov6_udUZc=w1024-rw',
  'https://lh3.googleusercontent.com/60sOHC5VtavY-NcAQSlhJT46ifMVbPwbfAJ4gB6N3DHSMuSNkdKC8cdEs5p_l-L9QUcshlEsp_lTWYHx3f3ARU2gLHO03pntC_Lw9Vy3cINebH1fPg=w1024-rw',
  'https://lh3.googleusercontent.com/VV5Kb03VcbrR79Yr9NvEzOyeW-YqLKON9NYKlYP9H47WHUDtxdJLqnV59ZYWupAbGECsHbwr6xulDTvyc4eVzvOJmK8nl0dsVuDJaneJDHNpqu3i=w1024-rw',
  'https://lh3.googleusercontent.com/uMhb3MGmuJ72mWzjLmADnvbX9S-Bl5a6-nLtE3RN3YAGLJLnv3zRwiXbiZbSTMGfLQEkmZxJcyYNoGVTyZxjMNMH9hbFNGWlfwBdqa_ugLjEFSYITg=w1024-rw',
  'https://lh3.googleusercontent.com/PQvNM5oh9cdsyp3WyEVwm8tGqPTj41m2mGwI-kw-wlXuuets9rcGZ0s3X-rplWup9IaJ8D-S0cH_5NZtNem8rswqMp1WU25XKpdZEqgd_sUITr0al6c=w1024-rw',
  'https://lh3.googleusercontent.com/N-3_MptcAVRPPrPqy-3cP8I_i_evOebb36s00ifugl6CDsmxDWcQzfJSB3Qk2mryOvhqzb4Vv6t3CtWb80z9vU2cG0Q8P-ONy5KwoP9GygN2OlwM8tY=h1200-rw',
  'https://lh3.googleusercontent.com/89OLr5NEdWgL4wp9mAo88eH4BcVFaxZWdTXajqhfz_h0skGrK3fTIzQaMtwLepYQ5fNRu4vDy2f4zsa_RvX5OSLcgJqkUiKKSZBVMf0YB2C72BuNTUU=h1200-rw',
  'https://lh3.googleusercontent.com/cdzUZ2dHySgIJSKrZ_-fJyVLcMG8YktAjCFR2Mx8wz3fd-Vg9dTN266PteH-rczn7Pk-y9vPG-KRvCqbAbuNjF0wA681mZZwrOM_qT2bN0orVwtf4A=h1200-rw'
]
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)
const textToImageStore = useTextToImageStore()
const { textToImageResult, aiToolImageCardRef, prompt, loadings, errors }
  = storeToRefs(textToImageStore)

// Local state for selected images
const selectedImages = ref<ImageFile[]>([])

// Local state for person generation and safety filter
const personGeneration = ref('DONT_ALLOW')
const safetyFilterLevel = ref('BLOCK_LOW_AND_ABOVE')

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  model: models[0],
  style: 'Dynamic',
  imageDimension: '1:1',
  personGeneration: 'DONT_ALLOW',
  safetyFilterLevel: 'BLOCK_LOW_AND_ABOVE',
  selectedImages: [] as ImageFile[]
})

// Initialize initial values on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    model: model.value,
    style: style.value,
    imageDimension: imageDimension.value,
    personGeneration: personGeneration.value,
    safetyFilterLevel: safetyFilterLevel.value,
    selectedImages: [...selectedImages.value]
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = prompt.value !== initialValues.value.prompt
      || model.value?.value !== initialValues.value.model?.value
      || style.value !== initialValues.value.style
      || imageDimension.value !== initialValues.value.imageDimension
      || personGeneration.value !== initialValues.value.personGeneration
      || safetyFilterLevel.value !== initialValues.value.safetyFilterLevel

  // Image comparison with better performance
  const imagesChanged = compareImageArrays(
    selectedImages.value,
    initialValues.value.selectedImages
  )

  return basicFieldsChanged || imagesChanged
})

// Handle image selection
const handleImagesSelected = (images: ImageFile[]) => {
  selectedImages.value = images
  // Also update store for backward compatibility
  textToImageStore.selectedImages = images
}

// Handle try prompt from examples
const handleTryPrompt = (promptText: string) => {
  prompt.value = promptText
  // Scroll to the prompt input area
  nextTick(() => {
    const promptElement = document.querySelector('textarea[placeholder*="Describe the image"]')
    if (promptElement) {
      promptElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      promptElement.focus()
    }
  })
}

// Helper function to perform the actual generation
const performGeneration = async () => {
  // Extract File objects from selected images
  const files = selectedImages.value.map(img => img.file).filter(Boolean)

  const result = await textToImageStore.textToImage({
    prompt: prompt.value,
    model: model.value?.value || 'gemini-2.0-flash-exp-image-generation',
    style: style.value || 'Portrait',
    aspect_ratio: imageDimension.value || '1:1',
    files: files
  })

  if (result) {
    toast.add({
      id: 'success',
      title: 'Image Generation',
      description: 'Your image is ready!',
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      model: model.value,
      style: style.value,
      imageDimension: imageDimension.value,
      personGeneration: personGeneration.value,
      safetyFilterLevel: safetyFilterLevel.value,
      selectedImages: [...selectedImages.value]
    }
  }
}

const onGenerate = async () => {
  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value,
      t('Please enter a prompt to generate an image.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'image',
    hasChanges,
    hasResult: computed(() => !!textToImageResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('style')"
              :label="$t('style')"
            >
              <BaseImageStyleSelect
                v-model="style"
                class="w-full"
                size="sm"
              />
            </UFormField>
          </div>
          <UFormField :label="$t('Prompt')">
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="$t('Describe the image you want to generate...')"
              :rows="6"
            />
          </UFormField>
          <UFormField
            v-if="model?.options?.includes('imageDimensions')"
            :label="$t('aspectRatio')"
          >
            <BaseImageDimensionsSelect />
          </UFormField>
          <div
            v-if="model?.options?.includes('yourImage')"
            class="flex flex-row gap-3 items-end"
          >
            <UFormField :label="$t('Image Reference')">
              <BaseImageSelect
                v-model="selectedImages"
                @update:model-value="handleImagesSelected"
              />
            </UFormField>
            <BaseImageSelectedList
              v-model="selectedImages"
              @update:model-value="handleImagesSelected"
            />
          </div>
          <div class="flex justify-end gap-2 items-center flex-row">
            <div
              v-if="!runtimeConfig.public.features.beta"
              class="text-xs text-right"
            >
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("This generation will cost: {cost} Credits", {
                    cost:
                      getServicePriceByModelName(model?.value)
                        ?.effective_price || 0
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('generate')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-secondary-600 cursor-pointer"
              trailing-icon="line-md:arrow-right"
              :loading="loadings['textToImage']"
              :disabled="!prompt"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToImageResult || loadings['textToImage'])
            && !errors['textToImage']
        "
        ref="aiToolImageCardRef"
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolImageCard
          v-bind="textToImageResult"
          :data="textToImageResult"
          :loading="loadings['textToImage']"
          class="h-full"
        />
      </Motion>
      <UCard
        v-else
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div
          v-if="errors['textToImage']"
          class="flex flex-col items-center justify-center h-full"
        >
          <div>
            <UIcon
              name="i-lucide-alert-circle"
              class="text-6xl mb-2 text-error"
            />
          </div>
          <div class="text-sm text-error">
            {{ $t(errors["textToImage"] || "Something went wrong") }}
          </div>
        </div>
        <div
          v-else
          class="h-full"
        >
          <div class="mb-4 text-center">
            <h3
              class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
            >
              {{ $t("AI Image Generation Examples") }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{
                $t(
                  "Explore the power of AI image generation with these interactive comparisons"
                )
              }}
            </p>
          </div>
          <BaseImageComparisonExamples
            :height="'350px'"
            :autoplay="true"
            :interval="6000"
            @try-prompt="handleTryPrompt"
          />
        </div>
      </UCard>
    </div>
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.5,
        delay: 1
      }"
    >
      <UPageMarquee
        pause-on-hover
        class="py-2 -mx-4 sm:-mx-6 lg:-mx-8 [--duration:40s] mt-6"
      >
        <Motion
          v-for="(img, index) in aiPhotos"
          :key="index"
          :initial="{
            scale: 1.1,
            opacity: 0,
            filter: 'blur(20px)'
          }"
          :animate="{
            scale: 1,
            opacity: 1,
            filter: 'blur(0px)'
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.1
          }"
        >
          <img
            width="434"
            height="234"
            class="rounded-lg"
            :class="index % 2 === 0 ? '-rotate-2' : 'rotate-2'"
            :src="img"
          >
        </Motion>
      </UPageMarquee>
    </Motion>
  </UContainer>
</template>

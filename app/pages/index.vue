<script setup lang="ts">
// Landing page for AI video generation platform

// Set layout to fullscreen for immersive experience
definePageMeta({
  layout: 'fullscreen'
})

// SEO meta tags for landing page
useSeoMeta({
  title: 'GeminiGen AI - Tạo Video AI Chất Lượng <PERSON>',
  description: '<PERSON>yể<PERSON> đổi ý tưởng thành video AI đáng kinh ngạc chỉ trong vài giây. Công nghệ tiên tiến nhất để tạo nội dung video chuyên nghiệp.',
  ogTitle: 'GeminiGen AI - Tạo Video AI Chất Lượng Cao',
  ogDescription: 'Chuyển đổi ý tưởng thành video AI đáng kinh ngạc chỉ trong vài giây. Công nghệ tiên tiến nhất để tạo nội dung video chuyên nghiệp.',
  ogImage: '/og-image.jpg',
  twitterCard: 'summary_large_image'
})
</script>

<template>
  <div class="min-h-screen">
    <!-- Simple navigation for landing page -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border">
      <UContainer class="flex items-center justify-between py-4">
        <div class="flex items-center gap-3">
          <BaseLogo class="w-8 h-8" />
          <span class="text-xl font-bold">GeminiGen AI</span>
        </div>
        
        <div class="hidden md:flex items-center gap-6">
          <UButton
            label="Tính Năng"
            variant="ghost"
            color="gray"
            to="#features"
            class="scroll-smooth"
          />
          <UButton
            label="Cách Sử Dụng"
            variant="ghost"
            color="gray"
            to="#demo-section"
            class="scroll-smooth"
          />
          <UButton
            label="FAQ"
            variant="ghost"
            color="gray"
            to="#faq"
            class="scroll-smooth"
          />
          <UButton
            label="Bảng Giá"
            variant="ghost"
            color="gray"
            to="/pricing"
          />
        </div>

        <div class="flex items-center gap-3">
          <ColorModeButton />
          <UButton
            label="Vào Ứng Dụng"
            color="primary"
            variant="solid"
            to="/app"
            icon="mingcute:ai-fill"
          />
        </div>
      </UContainer>
    </nav>

    <!-- Hero Section -->
    <LandingHeroSection />

    <!-- Features Section -->
    <section id="features">
      <LandingFeaturesSection />
    </section>

    <!-- How to Create Section -->
    <LandingHowToSection />

    <!-- FAQ Section -->
    <section id="faq">
      <LandingFAQSection />
    </section>

    <!-- Footer -->
    <LandingFooter />
  </div>
</template>

<style scoped>
/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Navigation backdrop blur effect */
nav {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Ensure sections have proper spacing for fixed nav */
section {
  scroll-margin-top: 80px;
}
</style>
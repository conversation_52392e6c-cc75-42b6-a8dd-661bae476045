export default defineNuxtRouteMiddleware((to) => {
  const runtimeConfig = useRuntimeConfig()

  // List of routes that should be blocked when beta feature is disabled
  const betaOnlyRoutes = [
    '/profile/credits',
    '/profile/orders',
    '/pricing'
  ]

  // List of routes that should be blocked when beta feature is enabled
  const nonBetaRoutes = [
    '/profile/integration',
    '/profile/integration/api-keys',
    '/profile/integration/webhook'
  ]

  // Check if current route is beta-only and beta feature is disabled
  if (betaOnlyRoutes.includes(to.path) && !runtimeConfig.public.features.beta) {
    // Redirect to profile page for profile-related routes, or home for others
    if (to.path.startsWith('/profile/')) {
      return navigateTo('/profile')
    } else {
      return navigateTo('/')
    }
  }

  // Check if current route should be blocked when beta feature is enabled
  if (nonBetaRoutes.some(route => to.path.startsWith(route)) && runtimeConfig.public.features.beta) {
    return navigateTo('/profile')
  }
})
